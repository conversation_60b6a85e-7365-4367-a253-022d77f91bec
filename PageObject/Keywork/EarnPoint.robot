*** Settings ***
Library    SeleniumLibrary
Library    Collections
Resource   ../Locator/EarnPoint.robot


*** Keywords ***
Get All Earn Card Element
    [Arguments]     ${ELEMENT_CARD}
    ${elements}=    Get WebElements    ${ELEMENT_CARD}
    RETURN          ${elements}


Click All Earn Links
    [Arguments]     ${ELEMENT_CARD}

    ${elements}=    Get All Earn Card Element     ${ELEMENT_CARD}
    ${element_count}=    Get Length    ${elements}
    Sleep     1s
    FOR    ${index}    IN RANGE    ${element_count}
        ${original_handle}=    Get Window Handles
        ${original_handle}=    Get From List    ${original_handle}    0
        ${links}=    Get WebElements    ${ELEMENT_CARD}
        Click Element    ${links}[${index}]
        Sleep    1s
        ${all_handles}=    Get Window Handles
        FOR    ${handle}    IN    @{all_handles}
            Run Keyword If    '${handle}' != '${original_handle}'    Switch Window    ${handle}
        END
        Sleep    1s
        Close Window
        Switch Window    ${original_handle}
        Sleep    1s
    END




